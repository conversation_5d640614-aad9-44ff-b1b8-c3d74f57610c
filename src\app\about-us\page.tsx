import React from 'react';
import type { Metada<PERSON> } from "next";

export const metadata: Metadata = {
  title: "About Us - Steal a Brainrot Secrets",
  description: "Learn about Steal a Brainrot Secrets, your ultimate destination for mastering Roblox stealing strategies, advanced techniques, and professional gameplay guides.",
  alternates: {
    canonical: 'https://stealabrainrotsecrets.top/about-us',
  },
};

export default function AboutUs() {
  return (
    <main className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Title Section */}
        <h1 className="text-5xl font-bold text-center mb-4 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
          About Us
        </h1>
        <p className="text-gray-300 text-center mb-12 max-w-4xl mx-auto">
          Welcome to Steal a Brainrot Secrets, your ultimate destination for mastering Roblox stealing strategies, advanced techniques, and professional gameplay guides. Dive into the world of competitive gaming and join our community of elite players.
        </p>

        {/* Welcome Section */}
        <div className="mb-16 text-gray-300">
          <p className="mb-6">
            Welcome to <span className="text-purple-400 font-semibold">Steal a Brainrot Secrets</span> – your go-to place for everything related to steal a brainrot secrets, advanced strategies, and competitive gameplay mastery!
          </p>
          <p className="mb-6">
            At <span className="text-purple-400 font-semibold">Steal a Brainrot Secrets</span>, we are passionate about bringing you the most comprehensive steal a brainrot secrets and professional techniques for dominating Roblox's most competitive stealing game. Whether you&apos;re a seasoned player or a newcomer, we provide steal a brainrot secrets names, strategies, and insider knowledge to help you excel. Our focus is on offering steal a brainrot secrets list content that transforms players into legendary champions.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Our Mission
          </h2>
          <p className="text-gray-300">
            Our mission is to provide the ultimate steal a brainrot secrets resource for players, helping you master advanced stealing techniques, discover steal a brainrot secrets new strategies, and dominate the competitive Roblox gaming scene. We strive to connect elite players from around the world, offering steal a brainrot secrets wiki insights and professional tactics to transform your gameplay experience.
          </p>
        </div>

        {/* What We Offer Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            What We Offer
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                title: "Advanced Strategy Guides",
                description: "In-depth steal a brainrot secrets tutorials and professional techniques to help you master competitive gameplay and dominate every match."
              },
              {
                title: "Steal a Brainrot Secrets Names",
                description: "Learn the legendary steal a brainrot secrets names and techniques used by top players to achieve consistent victories and build elite reputations."
              },
              {
                title: "Professional Tactics Database",
                description: "Discover our comprehensive steal a brainrot secrets list featuring advanced strategies, timing techniques, and psychological warfare methods."
              },
              {
                title: "Community Championships",
                description: "Join our elite community and stay updated with steal a brainrot secrets new developments, tournaments, and competitive events."
              },
              {
                title: "Exclusive Content",
                description: "Access steal a brainrot secrets finder tools, insider tips, and exclusive content designed to elevate your gameplay to championship level."
              }
            ].map((item, index) => (
              <div key={index} className="bg-gray-900 rounded-lg p-6 border border-purple-600/30 hover:border-purple-600 transition-colors">
                <h3 className="text-xl font-bold mb-4 text-purple-400">{item.title}</h3>
                <p className="text-gray-300">{item.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Why Choose Us Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Why Choose Us
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Professional Expertise</h3>
              <p className="text-gray-300">
                We provide the most comprehensive steal a brainrot secrets and professional insights that help you achieve championship-level gameplay performance.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Strategic Innovation</h3>
              <p className="text-gray-300">
                Our steal a brainrot secrets wiki encourages you to experiment with advanced techniques and innovative strategies, unlocking your competitive potential.
              </p>
            </div>
            <div className="text-center">
              <h3 className="text-xl font-bold mb-4 text-purple-400">Elite Community</h3>
              <p className="text-gray-300">
                Connect with top players worldwide, share steal a brainrot secrets new discoveries, and join the excitement of competitive Roblox gaming mastery.
              </p>
            </div>
          </div>
        </div>

        {/* Get Involved Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Get Involved
          </h2>
          <p className="text-gray-300 mb-6">
            If you&apos;re ready to dive deeper into the world of competitive Roblox gaming and master steal a brainrot secrets, explore our comprehensive platform! Here, you&apos;ll find steal a brainrot secrets guides, professional tactics, and an elite community that&apos;s always ready to share advanced strategies and learn together.
          </p>
          <p className="text-gray-300">
            Thank you for visiting <span className="text-purple-400 font-semibold">Steal a Brainrot Secrets</span>—your destination for competitive mastery, strategic excellence, and championship success!
          </p>
        </div>

        {/* Contact Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-pink-500 via-purple-500 to-purple-700 text-transparent bg-clip-text">
            Contact Us
          </h2>
          <p className="text-gray-300 mb-4">
            Have any questions or feedback? We&apos;d love to hear from you!
          </p>
          <ul className="text-gray-300">
            <li className="mb-2">
              <span className="text-purple-400">Email:</span>{" "}
              <a href="mailto:<EMAIL>" className="hover:text-purple-400 transition-colors">
                <EMAIL>
              </a>
            </li>
            <li>
              <span className="text-purple-400">Social Media:</span> Follow us for updates and news about the game and community events.
            </li>
          </ul>
        </div>
      </div>
    </main>
  );
}